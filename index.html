<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickman Dragon Fight Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        
        .header {
            background-color: #2d2d2d;
            color: white;
            padding: 20px;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        
        .game-container {
            width: 100%;
            max-width: 1200px;
            height: 80vh;
            margin: 20px;
            border: 2px solid #444;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }
        
        .game-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .footer {
            color: #888;
            text-align: center;
            padding: 10px;
            font-size: 14px;
        }
        
        .error-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: white;
            text-align: center;
            padding: 40px;
            box-sizing: border-box;
        }

        .error-message h2 {
            color: #ff6b6b;
            margin-bottom: 20px;
        }

        .solutions {
            margin-top: 30px;
        }

        .solution-buttons {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .play-button, .popup-button {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .play-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .play-button:hover {
            background: linear-gradient(45deg, #45a049, #3d8b40);
            transform: translateY(-2px);
        }

        .popup-button {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
        }

        .popup-button:hover {
            background: linear-gradient(45deg, #1976D2, #1565C0);
            transform: translateY(-2px);
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .game-container {
                margin: 10px;
                height: 70vh;
            }

            .solution-buttons {
                flex-direction: column;
                align-items: center;
            }

            .play-button, .popup-button {
                width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐉 Stickman Dragon Fight</h1>
        <p>Epic stickman fighting game with dragon battles</p>
    </div>
    
    <div class="game-container">
        <div class="error-message">
            <h2>⚠️ Cannot Embed Game</h2>
            <p>The game cannot be embedded due to security restrictions from the host server.</p>
            <div class="solutions">
                <h3>Alternative Solutions:</h3>
                <div class="solution-buttons">
                    <a href="https://d3e0b244-198d-4e9b-9837-03eb9e42ad68.poki-gdn.com/df258d27-9eab-460b-9aa9-2c923d36418c/index.html?country=EG&ccpaApplies=0&url_referrer=https%3A%2F%2Fpoki.com%2F&tag=pg-5ef29c2a7702f876c5e0613036b819bda139bd0f&site_id=3&iso_lang=en&poki_url=https%3A%2F%2Fpoki.com%2Fen%2Fg%2Fstickman-dragon-fight&hoist=yes&nonPersonalized=n&cloudsavegames=n&familyFriendly=n&categories=3%2C6%2C9%2C80%2C905%2C927%2C929%2C1126%2C1140%2C1143%2C1147%2C1163%2C1190%2C1197&special_condition=landing&game_id=d3e0b244-198d-4e9b-9837-03eb9e42ad68&game_version_id=df258d27-9eab-460b-9aa9-2c923d36418c&inspector=0&csp=1"
                       target="_blank"
                       class="play-button">
                        🎮 Play Game in New Tab
                    </a>
                    <button onclick="openInPopup()" class="popup-button">
                        🪟 Open in Popup Window
                    </button>
                </div>
            </div>
        </div>

        <!-- Hidden iframe for fallback attempt -->
        <iframe
            class="game-iframe hidden"
            src="https://d3e0b244-198d-4e9b-9837-03eb9e42ad68.poki-gdn.com/df258d27-9eab-460b-9aa9-2c923d36418c/index.html?country=EG&ccpaApplies=0&url_referrer=https%3A%2F%2Fpoki.com%2F&tag=pg-5ef29c2a7702f876c5e0613036b819bda139bd0f&site_id=3&iso_lang=en&poki_url=https%3A%2F%2Fpoki.com%2Fen%2Fg%2Fstickman-dragon-fight&hoist=yes&nonPersonalized=n&cloudsavegames=n&familyFriendly=n&categories=3%2C6%2C9%2C80%2C905%2C927%2C929%2C1126%2C1140%2C1143%2C1147%2C1163%2C1190%2C1197&special_condition=landing&game_id=d3e0b244-198d-4e9b-9837-03eb9e42ad68&game_version_id=df258d27-9eab-460b-9aa9-2c923d36418c&inspector=0&csp=1"
            title="Stickman Dragon Fight Game"
            allowfullscreen
            allow="gamepad; microphone; camera"
            onload="handleIframeLoad()"
            onerror="handleIframeError()">
        </iframe>
    </div>
    
    <div class="footer">
        <p>Game hosted by Poki | Enjoy playing!</p>
    </div>

    <script>
        const gameUrl = "https://d3e0b244-198d-4e9b-9837-03eb9e42ad68.poki-gdn.com/df258d27-9eab-460b-9aa9-2c923d36418c/index.html?country=EG&ccpaApplies=0&url_referrer=https%3A%2F%2Fpoki.com%2F&tag=pg-5ef29c2a7702f876c5e0613036b819bda139bd0f&site_id=3&iso_lang=en&poki_url=https%3A%2F%2Fpoki.com%2Fen%2Fg%2Fstickman-dragon-fight&hoist=yes&nonPersonalized=n&cloudsavegames=n&familyFriendly=n&categories=3%2C6%2C9%2C80%2C905%2C927%2C929%2C1126%2C1140%2C1143%2C1147%2C1163%2C1190%2C1197&special_condition=landing&game_id=d3e0b244-198d-4e9b-9837-03eb9e42ad68&game_version_id=df258d27-9eab-460b-9aa9-2c923d36418c&inspector=0&csp=1";

        function openInPopup() {
            const popup = window.open(
                gameUrl,
                'StickmanDragonFight',
                'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
            );

            if (!popup) {
                alert('Popup blocked! Please allow popups for this site and try again.');
            }
        }

        function handleIframeLoad() {
            // If iframe loads successfully, show it and hide error message
            const iframe = document.querySelector('.game-iframe');
            const errorMessage = document.querySelector('.error-message');

            setTimeout(() => {
                try {
                    // Test if iframe content is accessible
                    iframe.contentWindow.location.href;
                    iframe.classList.remove('hidden');
                    errorMessage.style.display = 'none';
                } catch (e) {
                    // If we can't access iframe content, it's likely blocked
                    console.log('Iframe blocked by X-Frame-Options or CSP');
                }
            }, 1000);
        }

        function handleIframeError() {
            console.log('Iframe failed to load');
        }

        // Try to detect if iframe is blocked after a delay
        setTimeout(() => {
            const iframe = document.querySelector('.game-iframe');
            const errorMessage = document.querySelector('.error-message');

            try {
                // If we can access the iframe's document, it loaded successfully
                if (iframe.contentDocument || iframe.contentWindow.document) {
                    iframe.classList.remove('hidden');
                    errorMessage.style.display = 'none';
                }
            } catch (e) {
                // Iframe is blocked, keep showing error message
                console.log('Iframe access blocked');
            }
        }, 2000);
    </script>
</body>
</html>
